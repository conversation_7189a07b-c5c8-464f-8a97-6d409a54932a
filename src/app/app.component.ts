import { Component, OnInit, OnDestroy } from '@angular/core';
import { RouterOutlet, RouterLink, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil } from 'rxjs';

import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzAvatarModule } from 'ng-zorro-antd/avatar';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzMessageModule } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzModalModule } from 'ng-zorro-antd/modal';

import { AuthService } from './services/auth.service';
import { User } from './models/auth.model';

@Component({
  selector: 'app-root',
  imports: [
    CommonModule,
    RouterOutlet,
    RouterLink,
    NzIconModule,
    NzLayoutModule,
    NzMenuModule,
    NzButtonModule,
    NzAvatarModule,
    NzDropDownModule,
    NzDividerModule,
    NzMessageModule,
    NzModalModule
  ],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css'
})
export class AppComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  isCollapsed = false;
  isAuthenticated = false;
  currentUser: User | null = null;

  constructor(
    private router: Router,
    private authService: AuthService,
    private message: NzMessageService,
    private modal: NzModalService
  ) {}

  ngOnInit() {
    // S'abonner aux changements d'état d'authentification
    this.authService.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe(authState => {
        this.isAuthenticated = authState.isAuthenticated;
        this.currentUser = authState.user;
      });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Getters pour les templates
  get logoSrc() {
    if (this.isCollapsed) {
      return 'assets/images/logo-min.png';
    }
    return 'assets/images/logo.png';
  }

  get tenantName(): string {
    return this.currentUser?.tenantName || 'Workeem';
  }

  get managerName(): string {
    if (this.currentUser) {
      return `${this.currentUser.firstName} ${this.currentUser.lastName}`;
    }
    return 'Utilisateur';
  }

  get userAvatar(): string {
    return this.currentUser?.avatar || 'assets/images/avatar-default.png';
  }

  // Méthodes utilitaires
  isReservationRoute(): boolean {
    const url = this.router.url;
    return url.includes('/reservations') || url.includes('/reservation-form');
  }

  isLoginRoute(): boolean {
    return this.router.url === '/login';
  }

  // Méthodes d'authentification
  onLogout() {
    this.modal.confirm({
      nzTitle: 'Confirmer la déconnexion',
      nzContent: 'Êtes-vous sûr de vouloir vous déconnecter ?',
      nzOkText: 'Se déconnecter',
      nzOkType: 'primary',
      nzOkDanger: true,
      nzCancelText: 'Annuler',
      nzOnOk: () => {
        this.performLogout();
      }
    });
  }

  private performLogout() {
    this.authService.logout().subscribe({
      next: () => {
        this.message.success('Déconnexion réussie');
      },
      error: (error) => {
        console.error('Erreur lors de la déconnexion:', error);
        this.message.error('Erreur lors de la déconnexion');
      }
    });
  }

  // Méthodes de navigation
  navigateToProfile() {
    // TODO: Implémenter la navigation vers le profil utilisateur
    this.message.info('Fonctionnalité de profil à venir');
  }

  navigateToSettings() {
    // TODO: Implémenter la navigation vers les paramètres
    this.message.info('Fonctionnalité de paramètres à venir');
  }
}
