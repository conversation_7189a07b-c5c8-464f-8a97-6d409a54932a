<div class="login-container">
  <!-- Background avec effet de gradient -->
  <div class="login-background">
    <div class="gradient-overlay"></div>
  </div>

  <!-- Contenu principal -->
  <div class="login-content">

    <!-- Logo et titre -->
    <div class="login-header">
      <div class="logo-container">
        <img [src]="appLogo" alt="Workeem Logo" class="logo">
      </div>
      <h1 class="app-name">{{ appName }}</h1>
      <p class="login-subtitle">Connectez-vous à votre espace de gestion</p>
    </div>

    <!-- Alerte de session expirée -->
    <nz-alert
      *ngIf="sessionExpired"
      nzType="warning"
      nzMessage="Session expirée"
      nzDescription="Votre session a expiré pour des raisons de sécurité. Veuillez vous reconnecter."
      nzShowIcon
      nzCloseable
      class="session-alert"
    ></nz-alert>

    <!-- Alerte d'erreur -->
    <nz-alert
      *ngIf="error"
      nzType="error"
      [nzMessage]="error"
      nzShowIcon
      nzCloseable
      (nzOnClose)="clearError()"
      class="error-alert"
    ></nz-alert>

    <!-- Formulaire de connexion -->
    <div class="login-form-container">
      <form nz-form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">

        <!-- Champ Email -->
        <nz-form-item>
          <nz-form-control
            [nzErrorTip]="getFieldError('email')"
            [nzValidateStatus]="isFieldInvalid('email') ? 'error' : ''"
          >
            <nz-input-group nzPrefixIcon="mail" nzSize="large">
              <input
                nz-input
                formControlName="email"
                type="email"
                placeholder="Adresse email"
                autocomplete="email"
                [class.error-input]="isFieldInvalid('email')"
              />
            </nz-input-group>
          </nz-form-control>
        </nz-form-item>

        <!-- Champ Mot de passe -->
        <nz-form-item>
          <nz-form-control
            [nzErrorTip]="getFieldError('password')"
            [nzValidateStatus]="isFieldInvalid('password') ? 'error' : ''"
          >
            <nz-input-group nzPrefixIcon="lock" nzSize="large">
              <input
                nz-input
                formControlName="password"
                [type]="showPassword ? 'text' : 'password'"
                placeholder="Mot de passe"
                autocomplete="current-password"
                [class.error-input]="isFieldInvalid('password')"
              />
              <span
                nz-input-suffix
                class="password-toggle"
                (click)="togglePasswordVisibility()"
              >
                <nz-icon
                  [nzType]="showPassword ? 'eye-invisible' : 'eye'"
                  nzTheme="outline"
                ></nz-icon>
              </span>
            </nz-input-group>
          </nz-form-control>
        </nz-form-item>

        <!-- Se souvenir de moi -->
        <nz-form-item class="remember-me-item">
          <nz-form-control>
            <label nz-checkbox formControlName="rememberMe" class="remember-me-checkbox">
              Se souvenir de moi
            </label>
          </nz-form-control>
        </nz-form-item>

        <!-- Bouton de connexion -->
        <nz-form-item class="login-button-item">
          <button
            nz-button
            nzType="primary"
            nzSize="large"
            nzBlock
            [nzLoading]="loading"
            [disabled]="loading"
            class="login-button"
            type="submit"
          >
            <span *ngIf="!loading">Se connecter</span>
            <span *ngIf="loading">Connexion en cours...</span>
          </button>
        </nz-form-item>

      </form>
    </div>



    <!-- Footer -->
    <div class="login-footer">
      <p>&copy; 2024 {{ appName }}. Tous droits réservés.</p>
    </div>

  </div>
</div>
