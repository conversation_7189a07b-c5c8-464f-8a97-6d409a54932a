import { Component, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';

import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzMessageModule } from 'ng-zorro-antd/message';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzAlertModule } from 'ng-zorro-antd/alert';

import { AuthService } from '../../services/auth.service';
import { LoginRequest, AuthError, AuthErrorCode } from '../../models/auth.model';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzFormModule,
    NzInputModule,
    NzButtonModule,
    NzCheckboxModule,
    NzIconModule,
    NzMessageModule,
    NzSpinModule,
    NzAlertModule
  ],
  templateUrl: './login.component.html',
  styleUrl: './login.component.css'
})
export class LoginComponent implements OnInit {
  loginForm!: FormGroup;

  // Signaux pour la réactivité
  private loadingSignal = signal<boolean>(false);
  private errorSignal = signal<string | null>(null);
  private showPasswordSignal = signal<boolean>(false);

  // Getters pour les templates
  get loading() { return this.loadingSignal(); }
  get error() { return this.errorSignal(); }
  get showPassword() { return this.showPasswordSignal(); }

  // Informations de l'application
  appName = 'Workeem';
  appLogo = 'assets/images/logo.png';

  // Messages d'état
  sessionExpired = false;

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute,
    private message: NzMessageService
  ) {
    this.initializeForm();
  }

  ngOnInit() {
    // Vérifier si l'utilisateur est déjà connecté
    if (this.authService.isLoggedIn()) {
      this.redirectAfterLogin();
      return;
    }

    // Vérifier si la session a expiré
    this.route.queryParams.subscribe(params => {
      if (params['expired'] === 'true') {
        this.sessionExpired = true;
        this.errorSignal.set('Votre session a expiré. Veuillez vous reconnecter.');
      }
    });
  }

  private initializeForm() {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      rememberMe: [false]
    });
  }

  onSubmit() {
    if (this.loginForm.valid) {
      this.performLogin();
    } else {
      this.markFormGroupTouched();
    }
  }

  private performLogin() {
    this.loadingSignal.set(true);
    this.errorSignal.set(null);

    const credentials: LoginRequest = {
      email: this.loginForm.value.email,
      password: this.loginForm.value.password,
      rememberMe: this.loginForm.value.rememberMe
    };

    this.authService.login(credentials).subscribe({
      next: (response) => {
        this.loadingSignal.set(false);
        this.message.success(`Bienvenue ${response.user.firstName} !`);
        this.redirectAfterLogin();
      },
      error: (error: AuthError) => {
        this.loadingSignal.set(false);
        this.handleLoginError(error);
      }
    });
  }

  private handleLoginError(error: AuthError) {
    let errorMessage = 'Une erreur est survenue lors de la connexion';

    switch (error.code) {
      case AuthErrorCode.INVALID_CREDENTIALS:
        errorMessage = 'Email ou mot de passe incorrect';
        break;
      case AuthErrorCode.ACCOUNT_LOCKED:
        errorMessage = 'Compte verrouillé. Contactez l\'administrateur.';
        break;
      case AuthErrorCode.ACCOUNT_DISABLED:
        errorMessage = 'Compte désactivé. Contactez l\'administrateur.';
        break;
      case AuthErrorCode.NETWORK_ERROR:
        errorMessage = 'Erreur de connexion. Vérifiez votre connexion internet.';
        break;
      default:
        errorMessage = error.message || errorMessage;
    }

    this.errorSignal.set(errorMessage);
    this.message.error(errorMessage);
  }

  private redirectAfterLogin() {
    // Récupérer l'URL de redirection stockée
    const redirectUrl = localStorage.getItem('workeem_redirect_url') || '/welcome';
    localStorage.removeItem('workeem_redirect_url');

    this.router.navigate([redirectUrl]);
  }

  private markFormGroupTouched() {
    Object.keys(this.loginForm.controls).forEach(key => {
      const control = this.loginForm.get(key);
      if (control) {
        control.markAsTouched();
        control.updateValueAndValidity();
      }
    });
  }

  togglePasswordVisibility() {
    this.showPasswordSignal.set(!this.showPassword);
  }

  clearError() {
    this.errorSignal.set(null);
  }

  // Méthodes utilitaires pour la validation
  isFieldInvalid(fieldName: string): boolean {
    const field = this.loginForm.get(fieldName);
    return !!(field && field.invalid && field.touched);
  }

  getFieldError(fieldName: string): string {
    const field = this.loginForm.get(fieldName);
    if (field && field.errors && field.touched) {
      if (field.errors['required']) {
        return `${this.getFieldLabel(fieldName)} est requis`;
      }
      if (field.errors['email']) {
        return 'Format d\'email invalide';
      }
      if (field.errors['minlength']) {
        return `${this.getFieldLabel(fieldName)} doit contenir au moins ${field.errors['minlength'].requiredLength} caractères`;
      }
    }
    return '';
  }

  private getFieldLabel(fieldName: string): string {
    const labels: { [key: string]: string } = {
      email: 'Email',
      password: 'Mot de passe'
    };
    return labels[fieldName] || fieldName;
  }
}
