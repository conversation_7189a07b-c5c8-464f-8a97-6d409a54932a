/* Page container */
.space-details-page {
  padding: 0;
  min-height: 100vh;
}

/* Page content */
.page-content {
  max-width: 1200px;
  margin: 0 auto;
}

/* Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding: 32px 0;
  border-bottom: 1px solid #E5E5EA;
}

.header-content {
  flex: 1;
}

.space-header-info {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.space-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #6E56CF 0%, #8B7ED8 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 28px;
  flex-shrink: 0;
}

.space-title-section {
  flex: 1;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: #1C1C1E;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.page-description {
  font-size: 16px;
  color: #8E8E93;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.space-status {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.back-button,
.edit-button {
  border-radius: 8px !important;
  height: 40px !important;
  padding: 0 20px !important;
  font-weight: 500 !important;
}

.reserve-button {
  border-radius: 8px !important;
  height: 40px !important;
  padding: 0 20px !important;
  font-weight: 500 !important;
  background: linear-gradient(135deg, #6E56CF 0%, #8B7ED8 100%) !important;
  border: none !important;
}

/* Space details container */
.space-details-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-bottom: 32px;
}

/* Cards */
.info-card,
.equipment-card,
.amenities-card,
.rules-card,
.calendar-card {
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  border: 1px solid #E5E5EA !important;
}

.info-card .ant-card-head,
.equipment-card .ant-card-head,
.amenities-card .ant-card-head,
.rules-card .ant-card-head,
.calendar-card .ant-card-head {
  border-bottom: 1px solid #E5E5EA !important;
  background: #FAFAFA !important;
}

.info-card .ant-card-head-title,
.equipment-card .ant-card-head-title,
.amenities-card .ant-card-head-title,
.rules-card .ant-card-head-title,
.calendar-card .ant-card-head-title {
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #1C1C1E !important;
}

/* Equipment grid */
.equipment-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.equipment-item {
  padding: 16px;
  border: 1px solid #E5E5EA;
  border-radius: 8px;
  background: #FAFAFA;
}

.equipment-info h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1C1C1E;
  margin: 0 0 8px 0;
}

.equipment-info p {
  font-size: 14px;
  color: #8E8E93;
  margin: 0 0 12px 0;
}

.equipment-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.equipment-quantity {
  font-size: 14px;
  color: #6E56CF;
  font-weight: 500;
}

/* Amenities */
.amenities-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.amenity-tag {
  font-size: 14px !important;
  border-radius: 6px !important;
  padding: 4px 12px !important;
  background: #F0F0F5 !important;
  color: #6E56CF !important;
  border: 1px solid #E5E5EA !important;
}

/* Rules */
.rules-list {
  margin: 0;
  padding-left: 20px;
}

.rules-list li {
  font-size: 14px;
  color: #1C1C1E;
  line-height: 1.6;
  margin-bottom: 8px;
}

.rules-list li:last-child {
  margin-bottom: 0;
}

/* Calendar section */
.calendar-section {
  margin-bottom: 32px;
}

.calendar-filters {
  margin-bottom: 20px;
  padding: 16px;
  background: #FAFAFA;
  border-radius: 8px;
  border: 1px solid #E5E5EA;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-item label {
  font-size: 14px;
  font-weight: 600;
  color: #1C1C1E;
  margin: 0;
}

/* Calendar container */
.calendar-container {
  margin-top: 20px;
}

/* FullCalendar customization */
.calendar-container ::ng-deep .fc {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.calendar-container ::ng-deep .fc-header-toolbar {
  margin-bottom: 20px;
  padding: 0 10px;
}

.calendar-container ::ng-deep .fc-button-group {
  gap: 4px !important;
}

.calendar-container ::ng-deep .fc-button {
  margin: 0 2px !important;
}

.calendar-container ::ng-deep .fc-toolbar-title {
  font-size: 20px !important;
  font-weight: 600 !important;
  color: #1C1C1E !important;
}

.calendar-container ::ng-deep .fc-button-primary {
  background: #6E56CF !important;
  border-color: #6E56CF !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
  padding: 6px 12px !important;
  font-size: 14px !important;
}

.calendar-container ::ng-deep .fc-button-primary:hover {
  background: #5A47B8 !important;
  border-color: #5A47B8 !important;
}

.calendar-container ::ng-deep .fc-button-primary:focus {
  box-shadow: 0 0 0 2px rgba(110, 86, 207, 0.2) !important;
}

.calendar-container ::ng-deep .fc-button-primary:disabled {
  background: #D9D9D9 !important;
  border-color: #D9D9D9 !important;
}

.calendar-container ::ng-deep .fc-today-button {
  background: #F0F0F5 !important;
  border-color: #E5E5EA !important;
  color: #6E56CF !important;
}

.calendar-container ::ng-deep .fc-today-button:hover {
  background: #E5E5EA !important;
  border-color: #D9D9D9 !important;
}

/* Calendar grid */
.calendar-container ::ng-deep .fc-daygrid-day {
  border-color: #E5E5EA !important;
}

.calendar-container ::ng-deep .fc-daygrid-day-top {
  padding: 8px !important;
}

.calendar-container ::ng-deep .fc-day-today {
  background: rgba(110, 86, 207, 0.05) !important;
}

.calendar-container ::ng-deep .fc-daygrid-day-number {
  color: #1C1C1E !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  padding: 4px !important;
}

/* Events */
.calendar-container ::ng-deep .fc-event {
  border-radius: 4px !important;
  border: none !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  padding: 2px 6px !important;
  margin: 1px 0 !important;
  cursor: pointer !important;
}

.calendar-container ::ng-deep .fc-event:hover {
  opacity: 0.8 !important;
}

.calendar-container ::ng-deep .fc-event-title {
  font-weight: 500 !important;
}

/* Time grid */
.calendar-container ::ng-deep .fc-timegrid-slot {
  border-color: #F0F0F0 !important;
  height: 30px !important;
}

.calendar-container ::ng-deep .fc-timegrid-slot-label {
  font-size: 12px !important;
  color: #8E8E93 !important;
}

.calendar-container ::ng-deep .fc-timegrid-axis {
  border-color: #E5E5EA !important;
}

.calendar-container ::ng-deep .fc-timegrid-divider {
  border-color: #E5E5EA !important;
}

/* List view */
.calendar-container ::ng-deep .fc-list-event {
  border-left: 4px solid !important;
  padding: 8px 12px !important;
}

.calendar-container ::ng-deep .fc-list-event-title {
  font-weight: 500 !important;
  color: #1C1C1E !important;
}

.calendar-container ::ng-deep .fc-list-event-time {
  color: #6E56CF !important;
  font-weight: 500 !important;
}

/* Responsive design */
@media (max-width: 768px) {
  .space-details-page {
    padding: 0 20px;
  }

  .page-header {
    flex-direction: column;
    gap: 20px;
    text-align: left;
    padding: 20px 0;
  }

  .space-header-info {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .header-actions {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
  }

  .page-title {
    font-size: 24px;
  }

  .equipment-grid {
    grid-template-columns: 1fr;
  }

  .calendar-container ::ng-deep .fc-header-toolbar {
    flex-direction: column;
    gap: 10px;
  }

  .calendar-container ::ng-deep .fc-toolbar-chunk {
    display: flex;
    justify-content: center;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .space-details-page {
    background: #1C1C1E;
    color: #FFFFFF;
  }

  .page-title {
    color: #FFFFFF;
  }

  .page-description {
    color: #8E8E93;
  }

  .equipment-item {
    background: #2C2C2E;
    border-color: #3A3A3C;
  }

  .equipment-info h4 {
    color: #FFFFFF;
  }

  .rules-list li {
    color: #FFFFFF;
  }

  .calendar-filters {
    background: #2C2C2E;
    border-color: #3A3A3C;
  }

  .filter-item label {
    color: #FFFFFF;
  }
}