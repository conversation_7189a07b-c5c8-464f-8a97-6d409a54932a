<div class="space-details-page">
  <!-- Loading state -->
  <nz-spin [nzSpinning]="loading" nzTip="Chargement des détails de l'espace...">

    <!-- Error state -->
    <nz-result
      *ngIf="error && !loading"
      nzStatus="error"
      [nzTitle]="error"
      nzSubTitle="Impossible de charger les détails de l'espace"
    >
      <div nz-result-extra>
        <button nz-button nzType="primary" (click)="onBack()">
          <nz-icon nzType="arrow-left"></nz-icon>
          Retour
        </button>
      </div>
    </nz-result>

    <!-- Content -->
    <div class="page-content" *ngIf="space && !loading && !error">

      <!-- En-tête -->
      <div class="page-header">
        <div class="header-content">
          <div class="space-header-info">
            <div class="space-icon">
              <nz-icon [nzType]="getSpaceIcon(space.type)" nzTheme="outline"></nz-icon>
            </div>
            <div class="space-title-section">
              <h1 class="page-title">{{ space.name }}</h1>
              <p class="page-description">
                <nz-icon nzType="environment" nzTheme="outline"></nz-icon>
                {{ space.location }}
                <span *ngIf="space.floor"> - {{ space.floor }}</span>
              </p>
              <div class="space-status">
                <nz-tag [nzColor]="getStatusColor(space.status)">
                  {{ getStatusLabel(space.status) }}
                </nz-tag>
                <nz-tag [nzColor]="getTypeColor(space.type)">
                  {{ getTypeLabel(space.type) }}
                </nz-tag>
              </div>
            </div>
          </div>
        </div>
        <div class="header-actions">
          <button nz-button nzType="default" nzSize="large" (click)="onBack()" class="back-button">
            <nz-icon nzType="arrow-left"></nz-icon>
            Retour
          </button>
          <button nz-button nzType="primary" nzSize="large" (click)="onNewReservation()" class="reserve-button">
            <nz-icon nzType="calendar"></nz-icon>
            Réserver
          </button>
          <button nz-button nzType="default" nzSize="large" (click)="onEdit()" class="edit-button">
            <nz-icon nzType="edit"></nz-icon>
            Modifier
          </button>
        </div>
      </div>

      <!-- Détails de l'espace -->
      <div class="space-details-container">

        <!-- Informations générales -->
        <nz-card class="info-card" nzTitle="Informations générales">
          <nz-descriptions nzBordered [nzColumn]="2">
            <nz-descriptions-item nzTitle="Description" [nzSpan]="2">
              {{ space.description }}
            </nz-descriptions-item>
            <nz-descriptions-item nzTitle="Capacité">
              <nz-icon nzType="team" nzTheme="outline"></nz-icon>
              {{ space.capacity }} personne(s)
            </nz-descriptions-item>
            <nz-descriptions-item nzTitle="Surface">
              <nz-icon nzType="expand" nzTheme="outline"></nz-icon>
              {{ space.area }} m²
            </nz-descriptions-item>
            <nz-descriptions-item nzTitle="Tarif horaire">
              <nz-icon nzType="euro" nzTheme="outline"></nz-icon>
              {{ space.pricing.hourlyRate }}{{ space.pricing.currency }}/h
            </nz-descriptions-item>
            <nz-descriptions-item nzTitle="Tarif journalier">
              <nz-icon nzType="calendar" nzTheme="outline"></nz-icon>
              {{ space.pricing.dailyRate }}{{ space.pricing.currency }}/jour
            </nz-descriptions-item>
          </nz-descriptions>
        </nz-card>

        <!-- Équipements -->
        <nz-card class="equipment-card" nzTitle="Équipements disponibles" *ngIf="space.equipment.length > 0">
          <div class="equipment-grid">
            <div class="equipment-item" *ngFor="let equipment of space.equipment">
              <div class="equipment-info">
                <h4>{{ equipment.name }}</h4>
                <p *ngIf="equipment.description">{{ equipment.description }}</p>
                <div class="equipment-details">
                  <span class="equipment-quantity">Quantité: {{ equipment.quantity }}</span>
                  <nz-tag [nzColor]="equipment.status === EquipmentStatus.WORKING ? 'green' : 'orange'">
                    {{ equipment.status === EquipmentStatus.WORKING ? 'Fonctionnel' :
                       equipment.status === EquipmentStatus.BROKEN ? 'En panne' : 'Maintenance' }}
                  </nz-tag>
                </div>
              </div>
            </div>
          </div>
        </nz-card>

        <!-- Commodités -->
        <nz-card class="amenities-card" nzTitle="Commodités" *ngIf="space.amenities.length > 0">
          <div class="amenities-list">
            <nz-tag *ngFor="let amenity of space.amenities" class="amenity-tag">
              {{ amenity }}
            </nz-tag>
          </div>
        </nz-card>

        <!-- Règles -->
        <nz-card class="rules-card" nzTitle="Règles d'utilisation" *ngIf="space.rules.length > 0">
          <ul class="rules-list">
            <li *ngFor="let rule of space.rules">{{ rule }}</li>
          </ul>
        </nz-card>

      </div>

      <nz-divider></nz-divider>

      <!-- Section Calendrier des réservations -->
      <div class="calendar-section">
        <nz-card class="calendar-card" nzTitle="Planning des réservations">

          <!-- Filtres -->
          <div class="calendar-filters">
            <div class="filter-item">
              <label>Statut des réservations</label>
              <nz-select
                [(ngModel)]="selectedStatus"
                nzPlaceHolder="Tous les statuts"
                nzAllowClear
                (ngModelChange)="onStatusFilterChange($event)"
                style="width: 200px;"
              >
                <nz-option nzLabel="Confirmée" nzValue="confirmed"></nz-option>
                <nz-option nzLabel="En attente" nzValue="pending"></nz-option>
                <nz-option nzLabel="Annulée" nzValue="cancelled"></nz-option>
                <nz-option nzLabel="Terminée" nzValue="completed"></nz-option>
                <nz-option nzLabel="Absence" nzValue="no_show"></nz-option>
              </nz-select>
            </div>
          </div>

          <!-- FullCalendar -->
          <div class="calendar-container">
            <full-calendar
              [options]="calendarOptions"
              #calendar
            ></full-calendar>
          </div>

        </nz-card>
      </div>

    </div>
  </nz-spin>
</div>
