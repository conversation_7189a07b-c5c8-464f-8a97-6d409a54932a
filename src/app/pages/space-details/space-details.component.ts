import { Component, OnInit, signal } from '@angular/core';
import { CommonModule, Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { FormsModule } from '@angular/forms';

import { NzCardModule } from 'ng-zorro-antd/card';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzResultModule } from 'ng-zorro-antd/result';
import { NzDescriptionsModule } from 'ng-zorro-antd/descriptions';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzMessageModule } from 'ng-zorro-antd/message';

import { FullCalendarModule } from '@fullcalendar/angular';
import { CalendarOptions, EventInput } from '@fullcalendar/core';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import listPlugin from '@fullcalendar/list';
import frLocale from '@fullcalendar/core/locales/fr';

import { Space, SpaceType, SpaceStatus, EquipmentStatus, SpaceReservation, ReservationStatus } from '../../models/space.model';
import { SpaceService } from '../../services/space.service';

@Component({
  selector: 'app-space-details',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NzCardModule,
    NzButtonModule,
    NzIconModule,
    NzTagModule,
    NzSpinModule,
    NzResultModule,
    NzDescriptionsModule,
    NzDividerModule,
    NzSelectModule,
    NzMessageModule,
    FullCalendarModule
  ],
  templateUrl: './space-details.component.html',
  styleUrl: './space-details.component.css'
})
export class SpaceDetailsComponent implements OnInit {
  // Signaux pour la réactivité
  private spaceSignal = signal<Space | null>(null);
  private reservationsSignal = signal<SpaceReservation[]>([]);
  private loadingSignal = signal<boolean>(false);
  private errorSignal = signal<string | null>(null);

  // Getters pour les templates
  get space() { return this.spaceSignal(); }
  get reservations() { return this.reservationsSignal(); }
  get loading() { return this.loadingSignal(); }
  get error() { return this.errorSignal(); }

  // Filtres
  selectedStatus: string | null = null;

  // Enums pour le template
  SpaceType = SpaceType;
  SpaceStatus = SpaceStatus;
  EquipmentStatus = EquipmentStatus;
  ReservationStatus = ReservationStatus;

  // FullCalendar options
  calendarOptions: CalendarOptions = {
    plugins: [dayGridPlugin, timeGridPlugin, interactionPlugin, listPlugin],
    headerToolbar: {
      left: 'prev,next today',
      center: 'title',
      right: 'dayGridMonth,timeGridWeek,timeGridDay,listWeek'
    },
    initialView: 'dayGridMonth',
    locale: frLocale,
    weekends: true,
    editable: false,
    selectable: false,
    dayMaxEvents: true,
    height: 'auto',
    businessHours: {
      daysOfWeek: [1, 2, 3, 4, 5, 6],
      startTime: '08:00',
      endTime: '20:00'
    },
    slotMinTime: '08:00:00',
    slotMaxTime: '20:00:00',
    allDaySlot: false,
    events: [],
    eventClick: this.handleEventClick.bind(this)
  };

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private location: Location,
    private spaceService: SpaceService,
    private message: NzMessageService
  ) {}

  ngOnInit() {
    const spaceId = this.route.snapshot.paramMap.get('id');
    if (spaceId) {
      this.loadSpaceDetails(spaceId);
    } else {
      this.errorSignal.set('ID d\'espace manquant');
    }
  }

  private loadSpaceDetails(spaceId: string) {
    this.loadingSignal.set(true);
    this.errorSignal.set(null);

    // Charger les détails de l'espace
    this.spaceService.getSpaceById(spaceId).subscribe({
      next: (space) => {
        if (space) {
          this.spaceSignal.set(space);
          this.loadSpaceReservations(spaceId);
        } else {
          this.errorSignal.set('Espace non trouvé');
          this.loadingSignal.set(false);
        }
      },
      error: (error) => {
        console.error('Erreur lors du chargement de l\'espace:', error);
        this.errorSignal.set('Erreur lors du chargement de l\'espace');
        this.loadingSignal.set(false);
      }
    });
  }

  private loadSpaceReservations(spaceId: string) {
    this.spaceService.getReservationsBySpace(spaceId).subscribe({
      next: (reservations) => {
        this.reservationsSignal.set(reservations);
        this.updateCalendarEvents();
        this.loadingSignal.set(false);
      },
      error: (error) => {
        console.error('Erreur lors du chargement des réservations:', error);
        this.message.error('Erreur lors du chargement des réservations');
        this.loadingSignal.set(false);
      }
    });
  }

  private updateCalendarEvents() {
    const events: EventInput[] = this.reservations
      .filter(reservation => this.matchesStatusFilter(reservation))
      .map(reservation => ({
        id: reservation.id,
        title: `${reservation.userName} - ${reservation.purpose}`,
        start: reservation.startTime,
        end: reservation.endTime,
        backgroundColor: this.getEventColor(reservation.status),
        borderColor: this.getEventColor(reservation.status),
        extendedProps: {
          reservation: reservation
        }
      }));

    this.calendarOptions = {
      ...this.calendarOptions,
      events: events
    };
  }

  private matchesStatusFilter(reservation: SpaceReservation): boolean {
    if (!this.selectedStatus) return true;
    return reservation.status === this.selectedStatus;
  }

  private getEventColor(status: ReservationStatus): string {
    switch (status) {
      case ReservationStatus.CONFIRMED: return '#52C41A';
      case ReservationStatus.PENDING: return '#FA8C16';
      case ReservationStatus.CANCELLED: return '#FF4D4F';
      case ReservationStatus.COMPLETED: return '#1890FF';
      case ReservationStatus.NO_SHOW: return '#F5222D';
      default: return '#D9D9D9';
    }
  }

  // Méthodes pour les filtres
  onStatusFilterChange(status: string | null) {
    this.selectedStatus = status;
    this.updateCalendarEvents();
  }

  // Handlers FullCalendar
  handleEventClick(clickInfo: any) {
    const reservation = clickInfo.event.extendedProps['reservation'];
    if (reservation) {
      this.router.navigate(['/reservations/details', reservation.id]);
    }
  }

  // Méthodes utilitaires pour le template
  getSpaceIcon(type: SpaceType): string {
    switch (type) {
      case SpaceType.WORKSTATION: return 'desktop';
      case SpaceType.PRIVATE_OFFICE: return 'home';
      case SpaceType.MEETING_ROOM: return 'team';
      case SpaceType.PHONE_BOOTH: return 'phone';
      case SpaceType.LOUNGE: return 'coffee';
      case SpaceType.CONFERENCE_ROOM: return 'video-camera';
      case SpaceType.HOT_DESK: return 'laptop';
      case SpaceType.DEDICATED_DESK: return 'user';
      case SpaceType.COLLABORATIVE: return 'cluster';
      default: return 'question';
    }
  }

  getTypeLabel(type: SpaceType): string {
    switch (type) {
      case SpaceType.WORKSTATION: return 'Poste de travail';
      case SpaceType.PRIVATE_OFFICE: return 'Bureau privé';
      case SpaceType.MEETING_ROOM: return 'Salle de réunion';
      case SpaceType.PHONE_BOOTH: return 'Cabine téléphonique';
      case SpaceType.LOUNGE: return 'Espace détente';
      case SpaceType.CONFERENCE_ROOM: return 'Salle de conférence';
      case SpaceType.HOT_DESK: return 'Bureau partagé';
      case SpaceType.DEDICATED_DESK: return 'Bureau dédié';
      case SpaceType.COLLABORATIVE: return 'Espace collaboratif';
      default: return 'Inconnu';
    }
  }

  getTypeColor(type: SpaceType): string {
    switch (type) {
      case SpaceType.WORKSTATION: return 'blue';
      case SpaceType.PRIVATE_OFFICE: return 'green';
      case SpaceType.MEETING_ROOM: return 'orange';
      case SpaceType.PHONE_BOOTH: return 'purple';
      case SpaceType.LOUNGE: return 'cyan';
      case SpaceType.CONFERENCE_ROOM: return 'red';
      case SpaceType.HOT_DESK: return 'geekblue';
      case SpaceType.DEDICATED_DESK: return 'gold';
      case SpaceType.COLLABORATIVE: return 'lime';
      default: return 'default';
    }
  }

  getStatusLabel(status: SpaceStatus): string {
    switch (status) {
      case SpaceStatus.AVAILABLE: return 'Disponible';
      case SpaceStatus.OCCUPIED: return 'Occupé';
      case SpaceStatus.MAINTENANCE: return 'Maintenance';
      case SpaceStatus.OUT_OF_ORDER: return 'Hors service';
      case SpaceStatus.RESERVED: return 'Réservé';
      default: return 'Inconnu';
    }
  }

  getStatusColor(status: SpaceStatus): string {
    switch (status) {
      case SpaceStatus.AVAILABLE: return 'success';
      case SpaceStatus.OCCUPIED: return 'warning';
      case SpaceStatus.MAINTENANCE: return 'processing';
      case SpaceStatus.OUT_OF_ORDER: return 'error';
      case SpaceStatus.RESERVED: return 'default';
      default: return 'default';
    }
  }

  // Actions
  onBack() {
    this.location.back();
  }

  onEdit() {
    if (this.space) {
      this.router.navigate(['/spaces/edit', this.space.id]);
    }
  }

  onNewReservation() {
    if (this.space) {
      this.router.navigate(['/reservation-form'], {
        queryParams: { spaceId: this.space.id }
      });
    }
  }
}
