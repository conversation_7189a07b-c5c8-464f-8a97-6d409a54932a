import { Component, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';

import { NzCardModule } from 'ng-zorro-antd/card';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzMessageModule } from 'ng-zorro-antd/message';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';

import { Invoice, BillingStats } from '../../models/invoice.model';
import { BillingService } from '../../services/billing.service';
import { jsPDF } from 'jspdf';

@Component({
  selector: 'app-billing',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NzCardModule,
    NzButtonModule,
    NzIconModule,
    NzSelectModule,
    NzInputModule,
    NzDatePickerModule,
    NzTableModule,
    NzTagModule,
    NzToolTipModule,
    NzModalModule,
    NzMessageModule,
    NzDropDownModule
  ],
  templateUrl: './billing.component.html',
  styleUrl: './billing.component.css'
})
export class BillingComponent implements OnInit {
  // Signaux pour la réactivité
  private loadingSignal = signal<boolean>(false);
  private invoicesSignal = signal<Invoice[]>([]);
  private statsSignal = signal<BillingStats>({
    totalRevenue: 0,
    monthlyRevenue: 0,
    pendingAmount: 0,
    overdueAmount: 0,
    totalInvoices: 0,
    paidInvoices: 0,
    pendingInvoices: 0,
    overdueInvoices: 0
  });

  // Getters pour les templates
  get loading() { return this.loadingSignal(); }
  get invoices() { return this.invoicesSignal(); }
  get stats() { return this.statsSignal(); }

  // Filtres
  selectedStatus: string | null = null;
  dateRange: Date[] | null = null;
  searchTerm: string = '';
  filteredInvoices: Invoice[] = [];

  constructor(
    private billingService: BillingService,
    private message: NzMessageService,
    private modal: NzModalService,
    private router: Router
  ) {}

  ngOnInit() {
    this.loadInvoices();
    this.loadStats();
  }

  private loadInvoices() {
    this.loadingSignal.set(true);
    this.billingService.getInvoices().subscribe({
      next: (invoices) => {
        this.invoicesSignal.set(invoices);
        this.applyFilters();
        this.loadingSignal.set(false);
      },
      error: (error) => {
        console.error('Erreur lors du chargement des factures:', error);
        this.message.error('Erreur lors du chargement des factures');
        this.loadingSignal.set(false);
      }
    });
  }

  private loadStats() {
    this.billingService.getBillingStats().subscribe({
      next: (stats) => {
        this.statsSignal.set(stats);
      },
      error: (error) => {
        console.error('Erreur lors du chargement des statistiques:', error);
      }
    });
  }

  // Méthodes de filtrage
  onStatusFilterChange(status: string | null) {
    this.selectedStatus = status;
    this.applyFilters();
  }

  onDateRangeChange(dateRange: Date[] | null) {
    this.dateRange = dateRange;
    this.applyFilters();
  }

  onSearchChange(searchTerm: string) {
    this.searchTerm = searchTerm;
    this.applyFilters();
  }

  private applyFilters() {
    let filtered = [...this.invoices];

    // Filtre par statut
    if (this.selectedStatus) {
      filtered = filtered.filter(invoice => invoice.status === this.selectedStatus);
    }

    // Filtre par date
    if (this.dateRange && this.dateRange.length === 2) {
      const [startDate, endDate] = this.dateRange;
      filtered = filtered.filter(invoice =>
        invoice.issueDate >= startDate && invoice.issueDate <= endDate
      );
    }

    // Filtre par recherche
    if (this.searchTerm) {
      const searchLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter(invoice =>
        invoice.memberName.toLowerCase().includes(searchLower) ||
        invoice.memberEmail.toLowerCase().includes(searchLower) ||
        invoice.invoiceNumber.toLowerCase().includes(searchLower)
      );
    }

    this.filteredInvoices = filtered;
  }

  clearFilters() {
    this.selectedStatus = null;
    this.dateRange = null;
    this.searchTerm = '';
    this.applyFilters();
  }

  // Actions sur les factures
  generateMonthlyInvoices() {
    this.modal.confirm({
      nzTitle: 'Générer les factures mensuelles',
      nzContent: 'Voulez-vous générer automatiquement les factures mensuelles pour tous les abonnés ?',
      nzOkText: 'Générer',
      nzCancelText: 'Annuler',
      nzCentered: true,
      nzOnOk: () => {
        this.billingService.generateMonthlyInvoices().subscribe({
          next: (invoices) => {
            this.message.success(`${invoices.length} facture(s) générée(s) avec succès`);
            this.loadInvoices();
            this.loadStats();
          },
          error: (error) => {
            console.error('Erreur lors de la génération:', error);
            this.message.error('Erreur lors de la génération des factures');
          }
        });
      }
    });
  }

  openNewInvoiceModal() {
    // Ici on ouvrirait un modal pour créer une nouvelle facture
    this.message.info('Fonctionnalité de création de facture à implémenter');
  }

  viewInvoice(invoice: Invoice) {
    this.router.navigate(['/billing', invoice.id]);
  }

  editInvoice(invoice: Invoice) {
    // Ici on ouvrirait un modal d'édition de la facture
    this.message.info(`Modifier la facture ${invoice.invoiceNumber}`);
  }

  markAsPaid(invoice: Invoice) {
    this.modal.confirm({
      nzTitle: 'Marquer comme payée',
      nzContent: `Voulez-vous marquer la facture ${invoice.invoiceNumber} comme payée ?`,
      nzOkText: 'Confirmer',
      nzCancelText: 'Annuler',
      nzOnOk: () => {
        this.billingService.markAsPaid(invoice.id, 'card').subscribe({
          next: (updatedInvoice) => {
            this.message.success('Facture marquée comme payée');
            this.loadInvoices();
            this.loadStats();
          },
          error: (error) => {
            console.error('Erreur lors de la mise à jour:', error);
            this.message.error('Erreur lors de la mise à jour');
          }
        });
      }
    });
  }

  deleteInvoice(invoice: Invoice) {
    this.modal.confirm({
      nzTitle: 'Supprimer la facture',
      nzContent: `Voulez-vous vraiment supprimer la facture ${invoice.invoiceNumber} ?`,
      nzOkText: 'Supprimer',
      nzOkType: 'primary',
      nzOkDanger: true,
      nzCancelText: 'Annuler',
      nzOnOk: () => {
        // Ici on supprimerait la facture
        this.message.success('Facture supprimée');
        this.loadInvoices();
        this.loadStats();
      }
    });
  }

  // Export des données
  exportToCSV() {
    const csvContent = this.billingService.exportToCSV(this.filteredInvoices);
    this.downloadFile(csvContent, 'factures.csv', 'text/csv');
    this.message.success('Export CSV téléchargé');
  }

  exportToPDF() {
    // Ici on générerait un PDF avec toutes les factures
    this.message.info('Export PDF en cours de développement');
  }

  downloadPDF(invoice: Invoice) {
    // Générer et télécharger le PDF de la facture (même méthode que dans billing-details)
    this.generateInvoicePDF(invoice);
    this.message.success(`Facture ${invoice.invoiceNumber} téléchargée`);
  }

  private generateInvoicePDF(invoice: Invoice) {
    // Utiliser la même logique que dans billing-details pour générer un vrai PDF
    const doc = new jsPDF();

    // Configuration
    const pageWidth = doc.internal.pageSize.width;
    const margin = 20;
    let yPosition = 30;

    // En-tête de l'entreprise
    doc.setFontSize(20);
    doc.setFont('helvetica', 'bold');
    doc.text('WORKEEM', margin, yPosition);

    doc.setFontSize(12);
    doc.setFont('helvetica', 'normal');
    doc.text('Espace de coworking moderne', margin, yPosition + 8);
    doc.text('Technopolis, Hay Riad, Rabat, Maroc', margin, yPosition + 16);
    doc.text('<EMAIL> | +212 5 37 12 34 56', margin, yPosition + 24);

    // Informations de la facture (côté droit)
    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text(`FACTURE ${invoice.invoiceNumber}`, pageWidth - margin - 60, yPosition);

    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    doc.text(`Date d'émission: ${invoice.issueDate.toLocaleDateString('fr-FR')}`, pageWidth - margin - 60, yPosition + 15);
    doc.text(`Date d'échéance: ${invoice.dueDate.toLocaleDateString('fr-FR')}`, pageWidth - margin - 60, yPosition + 25);

    // Ligne de séparation
    yPosition += 50;
    doc.line(margin, yPosition, pageWidth - margin, yPosition);

    // Informations client
    yPosition += 20;
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.text('Facturé à:', margin, yPosition);

    doc.setFont('helvetica', 'normal');
    doc.text(invoice.memberName, margin, yPosition + 15);
    doc.text(invoice.memberEmail, margin, yPosition + 25);
    if (invoice.memberCompany) {
      doc.text(invoice.memberCompany, margin, yPosition + 35);
      yPosition += 10;
    }

    // Tableau des articles
    yPosition += 50;
    doc.setFont('helvetica', 'bold');
    doc.text('Description', margin, yPosition);
    doc.text('Quantité', margin + 80, yPosition);
    doc.text('Prix unitaire', margin + 120, yPosition);
    doc.text('Total', pageWidth - margin - 30, yPosition);

    // Ligne sous les en-têtes
    yPosition += 5;
    doc.line(margin, yPosition, pageWidth - margin, yPosition);

    // Articles
    yPosition += 15;
    doc.setFont('helvetica', 'normal');
    invoice.items.forEach(item => {
      doc.text(item.description, margin, yPosition);
      doc.text(item.quantity.toString(), margin + 80, yPosition);
      doc.text(`${item.unitPrice.toFixed(2)} MAD`, margin + 120, yPosition);
      doc.text(`${item.total.toFixed(2)} MAD`, pageWidth - margin - 30, yPosition, { align: 'right' });
      yPosition += 15;
    });

    // Ligne de séparation avant totaux
    yPosition += 10;
    doc.line(margin + 80, yPosition, pageWidth - margin, yPosition);

    // Totaux
    yPosition += 15;
    doc.text(`Sous-total: ${invoice.subtotal.toFixed(2)} MAD`, pageWidth - margin - 60, yPosition);
    yPosition += 10;
    doc.text(`TVA (${invoice.taxRate}%): ${invoice.taxAmount.toFixed(2)} MAD`, pageWidth - margin - 60, yPosition);
    yPosition += 10;
    doc.setFont('helvetica', 'bold');
    doc.text(`TOTAL: ${invoice.total.toFixed(2)} MAD`, pageWidth - margin - 60, yPosition);

    // Statut de paiement
    yPosition += 20;
    doc.setFont('helvetica', 'normal');
    const statusText = invoice.status === 'paid' ? 'PAYÉE' :
                      invoice.status === 'overdue' ? 'EN RETARD' :
                      invoice.status === 'sent' ? 'ENVOYÉE' : 'BROUILLON';
    doc.text(`Statut: ${statusText}`, margin, yPosition);

    if (invoice.paidDate) {
      doc.text(`Date de paiement: ${invoice.paidDate.toLocaleDateString('fr-FR')}`, margin, yPosition + 10);
    }

    // Notes
    if (invoice.notes) {
      yPosition += 30;
      doc.setFont('helvetica', 'bold');
      doc.text('Notes:', margin, yPosition);
      doc.setFont('helvetica', 'normal');
      doc.text(invoice.notes, margin, yPosition + 10);
    }

    // Pied de page
    yPosition = doc.internal.pageSize.height - 40;
    doc.line(margin, yPosition, pageWidth - margin, yPosition);

    yPosition += 15;
    doc.setFontSize(10);
    doc.setFont('helvetica', 'italic');
    doc.text('Merci de votre confiance !', margin, yPosition);
    doc.text(`Document généré le ${new Date().toLocaleDateString('fr-FR')} à ${new Date().toLocaleTimeString('fr-FR')}`,
             pageWidth - margin - 80, yPosition);

    // Télécharger le PDF
    try {
      const pdfBlob = doc.output('blob');
      const url = window.URL.createObjectURL(pdfBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `facture-${invoice.invoiceNumber}.pdf`;
      link.click();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Erreur lors de la génération du PDF:', error);
      this.message.error('Erreur lors de la génération du PDF');
      // Fallback vers la méthode save() standard
      doc.save(`facture-${invoice.invoiceNumber}.pdf`);
    }
  }

  // Méthodes de partage
  shareViaWhatsApp(invoice: Invoice) {
    // Générer le PDF en blob pour le partage
    this.generatePDFBlob(invoice).then(blob => {
      // Créer un message WhatsApp avec les détails de la facture
      const message = `Bonjour ${invoice.memberName},\n\nVeuillez trouver ci-joint votre facture ${invoice.invoiceNumber} d'un montant de ${invoice.total.toFixed(2)} MAD.\n\nDate d'échéance: ${invoice.dueDate.toLocaleDateString('fr-FR')}\n\nCordialement,\nÉquipe WORKEEM`;

      // Encoder le message pour l'URL
      const encodedMessage = encodeURIComponent(message);

      // Ouvrir WhatsApp Web avec le message pré-rempli
      const whatsappUrl = `https://wa.me/?text=${encodedMessage}`;
      window.open(whatsappUrl, '_blank');

      // Télécharger aussi le PDF pour que l'utilisateur puisse l'envoyer manuellement
      this.downloadPDFBlob(blob, `facture-${invoice.invoiceNumber}.pdf`);

      this.message.success('Message WhatsApp préparé. Téléchargez le PDF et envoyez-le.');
    });
  }

  shareViaEmail(invoice: Invoice) {
    // Préparer l'email avec les détails de la facture
    const subject = `Facture ${invoice.invoiceNumber} - WORKEEM`;
    const body = `Bonjour ${invoice.memberName},

Veuillez trouver ci-joint votre facture ${invoice.invoiceNumber}.

Détails de la facture:
- Numéro: ${invoice.invoiceNumber}
- Date d'émission: ${invoice.issueDate.toLocaleDateString('fr-FR')}
- Date d'échéance: ${invoice.dueDate.toLocaleDateString('fr-FR')}
- Montant total: ${invoice.total.toFixed(2)} MAD
- Statut: ${this.getStatusLabel(invoice.status)}

Merci de procéder au paiement avant la date d'échéance.

Cordialement,
Équipe WORKEEM
<EMAIL>
+212 5 37 12 34 56`;

    // Encoder les paramètres pour l'URL mailto
    const encodedSubject = encodeURIComponent(subject);
    const encodedBody = encodeURIComponent(body);
    const encodedEmail = encodeURIComponent(invoice.memberEmail);

    // Créer l'URL mailto
    const mailtoUrl = `mailto:${encodedEmail}?subject=${encodedSubject}&body=${encodedBody}`;

    // Ouvrir le client email par défaut
    window.location.href = mailtoUrl;

    // Télécharger le PDF pour que l'utilisateur puisse l'attacher
    this.downloadPDF(invoice);

    this.message.success('Email préparé. Attachez le PDF téléchargé et envoyez l\'email.');
  }

  private async generatePDFBlob(invoice: Invoice): Promise<Blob> {
    const doc = new jsPDF();

    // Utiliser la même logique de génération que downloadPDF mais retourner un blob
    // Configuration
    const pageWidth = doc.internal.pageSize.width;
    const margin = 20;
    let yPosition = 30;

    // En-tête de l'entreprise
    doc.setFontSize(20);
    doc.setFont('helvetica', 'bold');
    doc.text('WORKEEM', margin, yPosition);

    doc.setFontSize(12);
    doc.setFont('helvetica', 'normal');
    doc.text('Espace de coworking moderne', margin, yPosition + 8);
    doc.text('Technopolis, Hay Riad, Rabat, Maroc', margin, yPosition + 16);
    doc.text('<EMAIL> | +212 5 37 12 34 56', margin, yPosition + 24);

    // Informations de la facture (côté droit)
    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text(`FACTURE ${invoice.invoiceNumber}`, pageWidth - margin - 60, yPosition);

    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    doc.text(`Date d'émission: ${invoice.issueDate.toLocaleDateString('fr-FR')}`, pageWidth - margin - 60, yPosition + 15);
    doc.text(`Date d'échéance: ${invoice.dueDate.toLocaleDateString('fr-FR')}`, pageWidth - margin - 60, yPosition + 25);

    // Ligne de séparation
    yPosition += 50;
    doc.line(margin, yPosition, pageWidth - margin, yPosition);

    // Informations client
    yPosition += 20;
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.text('Facturé à:', margin, yPosition);

    doc.setFont('helvetica', 'normal');
    doc.text(invoice.memberName, margin, yPosition + 15);
    doc.text(invoice.memberEmail, margin, yPosition + 25);
    if (invoice.memberCompany) {
      doc.text(invoice.memberCompany, margin, yPosition + 35);
      yPosition += 10;
    }

    // Tableau des articles
    yPosition += 50;
    doc.setFont('helvetica', 'bold');
    doc.text('Description', margin, yPosition);
    doc.text('Quantité', margin + 80, yPosition);
    doc.text('Prix unitaire', margin + 120, yPosition);
    doc.text('Total', pageWidth - margin - 30, yPosition);

    // Ligne sous les en-têtes
    yPosition += 5;
    doc.line(margin, yPosition, pageWidth - margin, yPosition);

    // Articles
    yPosition += 15;
    doc.setFont('helvetica', 'normal');
    invoice.items.forEach(item => {
      doc.text(item.description, margin, yPosition);
      doc.text(item.quantity.toString(), margin + 80, yPosition);
      doc.text(`${item.unitPrice.toFixed(2)} MAD`, margin + 120, yPosition);
      doc.text(`${item.total.toFixed(2)} MAD`, pageWidth - margin - 30, yPosition, { align: 'right' });
      yPosition += 15;
    });

    // Ligne de séparation avant totaux
    yPosition += 10;
    doc.line(margin + 80, yPosition, pageWidth - margin, yPosition);

    // Totaux
    yPosition += 15;
    doc.text(`Sous-total: ${invoice.subtotal.toFixed(2)} MAD`, pageWidth - margin - 60, yPosition);
    yPosition += 10;
    doc.text(`TVA (${invoice.taxRate}%): ${invoice.taxAmount.toFixed(2)} MAD`, pageWidth - margin - 60, yPosition);
    yPosition += 10;
    doc.setFont('helvetica', 'bold');
    doc.text(`TOTAL: ${invoice.total.toFixed(2)} MAD`, pageWidth - margin - 60, yPosition);

    // Statut de paiement
    yPosition += 20;
    doc.setFont('helvetica', 'normal');
    const statusText = invoice.status === 'paid' ? 'PAYÉE' :
                      invoice.status === 'overdue' ? 'EN RETARD' :
                      invoice.status === 'sent' ? 'ENVOYÉE' : 'BROUILLON';
    doc.text(`Statut: ${statusText}`, margin, yPosition);

    if (invoice.paidDate) {
      doc.text(`Date de paiement: ${invoice.paidDate.toLocaleDateString('fr-FR')}`, margin, yPosition + 10);
    }

    // Notes
    if (invoice.notes) {
      yPosition += 30;
      doc.setFont('helvetica', 'bold');
      doc.text('Notes:', margin, yPosition);
      doc.setFont('helvetica', 'normal');
      doc.text(invoice.notes, margin, yPosition + 10);
    }

    // Pied de page
    yPosition = doc.internal.pageSize.height - 40;
    doc.line(margin, yPosition, pageWidth - margin, yPosition);

    yPosition += 15;
    doc.setFontSize(10);
    doc.setFont('helvetica', 'italic');
    doc.text('Merci de votre confiance !', margin, yPosition);
    doc.text(`Document généré le ${new Date().toLocaleDateString('fr-FR')} à ${new Date().toLocaleTimeString('fr-FR')}`,
             pageWidth - margin - 80, yPosition);

    return doc.output('blob');
  }

  private downloadPDFBlob(blob: Blob, filename: string) {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
    window.URL.revokeObjectURL(url);
  }



  private generateInvoiceHTML(invoice: Invoice): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Facture ${invoice.invoiceNumber}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { text-align: center; margin-bottom: 40px; }
        .company-info { margin-bottom: 30px; }
        .invoice-details { display: flex; justify-content: space-between; margin-bottom: 10px; }
        .client-info { margin-bottom: 10px; }
        .items-table { width: 100%; border-collapse: collapse; margin-bottom: 30px; }
        .items-table th, .items-table td { border: 1px solid #ddd; padding: 12px; text-align: left; }
        .items-table th { background-color: #f5f5f5; }
        .totals { text-align: right; margin-bottom: 30px; }
        .footer { text-align: center; margin-top: 40px; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h1>WORKEEM</h1>
        <p>Espace de coworking moderne</p>
        <p>Technopolis, Hay Riad, Rabat, Maroc</p>
        <p><EMAIL> | +212 5 37 12 34 56</p>
    </div>

    <div class="company-info">
        <h2>FACTURE</h2>
    </div>

    <div class="invoice-details">
        <div>
            <strong>Numéro:</strong> ${invoice.invoiceNumber}<br>
            <strong>Date d'émission:</strong> ${invoice.issueDate.toLocaleDateString('fr-FR')}<br>
            <strong>Date d'échéance:</strong> ${invoice.dueDate.toLocaleDateString('fr-FR')}
        </div>
        <div>
            <strong>Statut:</strong> ${this.getStatusLabel(invoice.status)}
            ${invoice.paidDate ? `<br><strong>Date de paiement:</strong> ${invoice.paidDate.toLocaleDateString('fr-FR')}` : ''}
        </div>
    </div>

    <div class="client-info">
        <h3>Facturé à:</h3>
        <strong>${invoice.memberName}</strong><br>
        ${invoice.memberEmail}<br>
        ${invoice.memberCompany ? invoice.memberCompany + '<br>' : ''}
    </div>

    <table class="items-table">
        <thead>
            <tr>
                <th>Description</th>
                <th>Quantité</th>
                <th>Prix unitaire</th>
                <th>Total</th>
            </tr>
        </thead>
        <tbody>
            ${invoice.items.map(item => `
                <tr>
                    <td>${item.description}</td>
                    <td>${item.quantity}</td>
                    <td>${item.unitPrice.toFixed(2)} MAD</td>
                    <td>${item.total.toFixed(2)} MAD</td>
                </tr>
            `).join('')}
        </tbody>
    </table>

    <div class="totals">
        <p><strong>Sous-total:</strong> ${invoice.subtotal.toFixed(2)} MAD</p>
        <p><strong>TVA (${invoice.taxRate}%):</strong> ${invoice.taxAmount.toFixed(2)} MAD</p>
        <h3><strong>Total:</strong> ${invoice.total.toFixed(2)} MAD</h3>
    </div>

    <div class="footer">
        <p>Merci de votre confiance !</p>
        <p>Document généré le ${new Date().toLocaleDateString('fr-FR')} à ${new Date().toLocaleTimeString('fr-FR')}</p>
    </div>
</body>
</html>
    `;
  }

  private downloadFile(content: string, fileName: string, contentType: string) {
    const blob = new Blob([content], { type: contentType });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.click();
    window.URL.revokeObjectURL(url);
  }

  // Méthodes utilitaires
  getStatusColor(status: string): string {
    switch (status) {
      case 'draft': return 'default';
      case 'sent': return 'blue';
      case 'paid': return 'green';
      case 'overdue': return 'red';
      case 'cancelled': return 'orange';
      default: return 'default';
    }
  }

  getStatusLabel(status: string): string {
    switch (status) {
      case 'draft': return 'Brouillon';
      case 'sent': return 'Envoyée';
      case 'paid': return 'Payée';
      case 'overdue': return 'En retard';
      case 'cancelled': return 'Annulée';
      default: return status;
    }
  }

  // Navigation vers les détails du membre
  navigateToMemberDetail(memberId: string) {
    this.router.navigate(['/members', memberId]);
  }
}
