<div class="billing-container">
  <!-- En-tête -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">
        <nz-icon nzType="dollar" nzTheme="outline"></nz-icon>
        Facturation & Paiements
      </h1>
      <p class="page-description">Gérez vos factures, paiements et générez automatiquement la facturation mensuelle</p>
    </div>
    <div class="header-actions">
      <button nz-button nzType="default" nzSize="large" (click)="generateMonthlyInvoices()" class="generate-button">
        <nz-icon nzType="calendar"></nz-icon>
        Générer factures mensuelles
      </button>
      <button nz-button nzType="primary" nzSize="large" (click)="openNewInvoiceModal()" class="add-button">
        <nz-icon nzType="plus"></nz-icon>
        Nouvelle facture
      </button>
    </div>
  </div>

  <!-- Statistiques -->
  <div class="stats-section">
    <div class="stats-grid">
      <nz-card class="stat-card revenue">
        <div class="stat-content">
          <div class="stat-icon">
            <nz-icon nzType="rise" nzTheme="outline"></nz-icon>
          </div>
          <div class="stat-details">
            <div class="stat-value">{{ stats.totalRevenue | currency:'MAD ':'symbol':'1.0-0' }}</div>
            <div class="stat-label">Chiffre d'affaires total</div>
          </div>
        </div>
      </nz-card>

      <nz-card class="stat-card monthly">
        <div class="stat-content">
          <div class="stat-icon">
            <nz-icon nzType="calendar" nzTheme="outline"></nz-icon>
          </div>
          <div class="stat-details">
            <div class="stat-value">{{ stats.monthlyRevenue | currency:'MAD ':'symbol':'1.0-0' }}</div>
            <div class="stat-label">Revenus ce mois</div>
          </div>
        </div>
      </nz-card>

      <nz-card class="stat-card pending">
        <div class="stat-content">
          <div class="stat-icon">
            <nz-icon nzType="clock-circle" nzTheme="outline"></nz-icon>
          </div>
          <div class="stat-details">
            <div class="stat-value">{{ stats.pendingAmount | currency:'MAD ':'symbol':'1.0-0' }}</div>
            <div class="stat-label">En attente ({{ stats.pendingInvoices }})</div>
          </div>
        </div>
      </nz-card>

      <nz-card class="stat-card overdue">
        <div class="stat-content">
          <div class="stat-icon">
            <nz-icon nzType="exclamation-circle" nzTheme="outline"></nz-icon>
          </div>
          <div class="stat-details">
            <div class="stat-value">{{ stats.overdueAmount | currency:'MAD ':'symbol':'1.0-0' }}</div>
            <div class="stat-label">En retard ({{ stats.overdueInvoices }})</div>
          </div>
        </div>
      </nz-card>
    </div>
  </div>

  <!-- Filtres et actions -->
  <nz-card class="filters-card" nzTitle="Filtres et actions">
    <div class="filters-row">
      <div class="filter-item">
        <label>Statut</label>
        <nz-select
          [(ngModel)]="selectedStatus"
          nzPlaceHolder="Tous les statuts"
          nzAllowClear
          (ngModelChange)="onStatusFilterChange($event)"
        >
          <nz-option nzLabel="Brouillon" nzValue="draft"></nz-option>
          <nz-option nzLabel="Envoyée" nzValue="sent"></nz-option>
          <nz-option nzLabel="Payée" nzValue="paid"></nz-option>
          <nz-option nzLabel="En retard" nzValue="overdue"></nz-option>
          <nz-option nzLabel="Annulée" nzValue="cancelled"></nz-option>
        </nz-select>
      </div>

      <div class="filter-item">
        <label>Période</label>
        <nz-range-picker
          [(ngModel)]="dateRange"
          nzFormat="dd/MM/yyyy"
          (ngModelChange)="onDateRangeChange($event)"
        ></nz-range-picker>
      </div>

      <div class="filter-item">
        <label>Client</label>
        <nz-input-group nzSearch nzEnterButton="Rechercher">
          <input
            type="text"
            nz-input
            placeholder="Nom ou email du client"
            [(ngModel)]="searchTerm"
            (ngModelChange)="onSearchChange($event)"
          />
        </nz-input-group>
      </div>

      <div class="filter-actions">
        <button nz-button nzType="default" (click)="exportToCSV()">
          <nz-icon nzType="download"></nz-icon>
          Export CSV
        </button>
        <button nz-button nzType="default" (click)="exportToPDF()">
          <nz-icon nzType="file-pdf"></nz-icon>
          Export PDF
        </button>
        <button nz-button nzType="default" (click)="clearFilters()">
          <nz-icon nzType="clear"></nz-icon>
          Effacer
        </button>
      </div>
    </div>
  </nz-card>

  <!-- Liste des factures -->
  <nz-card class="invoices-card" nzTitle="Factures">
    <nz-table
      #invoicesTable
      [nzData]="filteredInvoices"
      [nzLoading]="loading"
      [nzPageSize]="10"
      [nzShowSizeChanger]="true"
      [nzShowQuickJumper]="true"
      [nzShowTotal]="totalTemplate"
    >
      <thead>
        <tr>
          <th nzSortKey="invoiceNumber">Numéro</th>
          <th nzSortKey="memberName">Client</th>
          <th nzSortKey="issueDate">Date émission</th>
          <th nzSortKey="dueDate">Date échéance</th>
          <th nzSortKey="status">Statut</th>
          <th nzSortKey="total">Montant</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let invoice of invoicesTable.data">
          <td>
            <strong>{{ invoice.invoiceNumber }}</strong>
          </td>
          <td>
            <div class="client-info" (click)="navigateToMemberDetail(invoice.memberId)" class="clickable-client">
              <div class="client-name">{{ invoice.memberName }}</div>
              <div class="client-email">{{ invoice.memberEmail }}</div>
              <div class="client-company" *ngIf="invoice.memberCompany">{{ invoice.memberCompany }}</div>
            </div>
          </td>
          <td>{{ invoice.issueDate | date:'dd/MM/yyyy' }}</td>
          <td>{{ invoice.dueDate | date:'dd/MM/yyyy' }}</td>
          <td>
            <nz-tag [nzColor]="getStatusColor(invoice.status)">
              {{ getStatusLabel(invoice.status) }}
            </nz-tag>
          </td>
          <td>
            <div class="amount-info">
              <div class="total-amount">{{ invoice.total | currency:'MAD ':'symbol':'1.2-2' }}</div>
              <div class="tax-info">dont {{ invoice.taxAmount | currency:'MAD ':'symbol':'1.2-2' }} de TVA</div>
            </div>
          </td>
          <td>
            <div class="action-buttons">
              <button nz-button nzType="text" nzSize="small" (click)="viewInvoice(invoice)" nz-tooltip="Voir">
                <nz-icon nzType="eye" nzTheme="outline"></nz-icon>
              </button>
              <button nz-button nzType="text" nzSize="small" (click)="editInvoice(invoice)" nz-tooltip="Modifier">
                <nz-icon nzType="edit" nzTheme="outline"></nz-icon>
              </button>
              <button
                nz-button
                nzType="text"
                nzSize="small"
                (click)="markAsPaid(invoice)"
                [nz-tooltip]="invoice.status === 'paid' ? 'Déjà payée' : 'Marquer comme payée'"
                [disabled]="invoice.status === 'paid'"
              >
                <nz-icon nzType="check-circle" nzTheme="outline"></nz-icon>
              </button>
              <button nz-button nzType="text" nzSize="small" (click)="downloadPDF(invoice)" nz-tooltip="Télécharger PDF">
                <nz-icon nzType="download" nzTheme="outline"></nz-icon>
              </button>
              <button nz-button nzType="text" nzSize="small" nz-dropdown [nzDropdownMenu]="shareMenu" nzPlacement="bottomRight" nz-tooltip="Partager">
                <nz-icon nzType="share-alt" nzTheme="outline"></nz-icon>
              </button>
              <nz-dropdown-menu #shareMenu="nzDropdownMenu">
                <ul nz-menu>
                  <li nz-menu-item (click)="shareViaWhatsApp(invoice)">
                    <nz-icon nzType="whats-app" nzTheme="outline"></nz-icon>
                    WhatsApp
                  </li>
                  <li nz-menu-item (click)="shareViaEmail(invoice)">
                    <nz-icon nzType="mail" nzTheme="outline"></nz-icon>
                    Email
                  </li>
                </ul>
              </nz-dropdown-menu>
              <button nz-button nzType="text" nzSize="small" nzDanger (click)="deleteInvoice(invoice)" nz-tooltip="Supprimer">
                <nz-icon nzType="delete" nzTheme="outline"></nz-icon>
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </nz-table>

    <ng-template #totalTemplate let-total>
      Total: {{ total }} facture(s)
    </ng-template>
  </nz-card>
</div>
