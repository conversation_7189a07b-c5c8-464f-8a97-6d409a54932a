<!-- Affichage conditionnel : layout complet si authentifié, sinon juste le router-outlet -->
<div *ngIf="!isAuthenticated || isLoginRoute(); else authenticatedLayout">
  <router-outlet></router-outlet>
</div>

<ng-template #authenticatedLayout>
<nz-layout class="app-layout" [class.sidebar-collapsed]="isCollapsed">
  <nz-sider class="menu-sidebar"
    nzCollapsible
    nzWidth="280px"
    nzBreakpoint="md"
    [(nzCollapsed)]="isCollapsed"
    [nzTrigger]="null"
  >
    <div class="sidebar-logo">
      <div class="logo-container">
        <img [src]="logoSrc" alt="Workeem Logo" class="logo-image" [class.logo-collapsed]="isCollapsed">
      </div>
    </div>

    <ul nz-menu nzTheme="light" nzMode="inline" [nzInlineCollapsed]="isCollapsed" class="main-menu">
      <!-- Tableau de bord -->
      <li nz-menu-item nzMatchRouter class="menu-item">
        <a routerLink="/welcome">
          <nz-icon nzType="dashboard" nzTheme="outline"></nz-icon>
          <span>Tableau de bord</span>
        </a>
      </li>

      <!-- Gestion des membres -->
      <li nz-menu-item nzMatchRouter class="menu-item">
        <a routerLink="/members">
          <nz-icon nzType="team" nzTheme="outline"></nz-icon>
          <span>Gestion des membres</span>
        </a>
      </li>

      <!-- Gestion des abonnements & formules -->
      <li nz-menu-item nzMatchRouter class="menu-item">
        <a routerLink="/subscriptions">
          <nz-icon nzType="credit-card" nzTheme="outline"></nz-icon>
          <span>Abonnements & formules</span>
        </a>
      </li>

      <!-- Gestion des espaces -->
      <li nz-menu-item nzMatchRouter class="menu-item">
        <a routerLink="/spaces">
          <nz-icon nzType="home" nzTheme="outline"></nz-icon>
          <span>Gestion des espaces</span>
        </a>
      </li>

      <!-- Réservations -->
      <li nz-menu-item [nzMatchRouter]="true" class="menu-item"
          [class.ant-menu-item-selected]="isReservationRoute()">
        <a routerLink="/reservations">
          <nz-icon nzType="calendar" nzTheme="outline"></nz-icon>
          <span>Réservations</span>
        </a>
      </li>

      <!-- Facturation & paiements -->
      <li nz-menu-item nzMatchRouter class="menu-item">
        <a routerLink="/billing">
          <nz-icon nzType="dollar" nzTheme="outline"></nz-icon>
          <span>Facturation & paiements</span>
        </a>
      </li>
    </ul>

    <!-- Tenant info au bas de la sidebar -->
    <div class="sidebar-footer" [class.collapsed]="isCollapsed">
      <div class="tenant-info">
        <div class="tenant-icon">
          <nz-icon nzType="home" nzTheme="outline"></nz-icon>
        </div>
        <div class="tenant-details" *ngIf="!isCollapsed">
          <div class="tenant-name">{{ tenantName }}</div>
          <div class="tenant-type">Espace de coworking</div>
        </div>
      </div>
    </div>
  </nz-sider>
  <nz-layout>
    <nz-header>
      <div class="app-header">
        <div class="header-left">
          <span class="header-trigger" (click)="isCollapsed = !isCollapsed">
            <nz-icon class="trigger" [nzType]="isCollapsed ? 'menu-unfold' : 'menu-fold'" />
          </span>
        </div>

        <div class="header-right">
          <div class="user-profile">
            <div class="user-info-container" nz-dropdown [nzDropdownMenu]="userMenu" nzPlacement="bottomRight">
                <nz-avatar
                  [nzSrc]="userAvatar"
                  [nzIcon]="'user'"
                  nzSize="default"
                  class="user-avatar-img"
                ></nz-avatar>
                <div class="user-info">
                  <span class="user-name">{{ managerName }}</span>
                  <span class="user-role">{{ currentUser?.role === 'admin' ? 'Administrateur' : 'Gestionnaire' }}</span>
                </div>
                <nz-icon nzType="down" nzTheme="outline" class="dropdown-icon"></nz-icon>
              </div>

            <nz-dropdown-menu #userMenu="nzDropdownMenu">
              <ul nz-menu>
                <li nz-menu-item (click)="navigateToProfile()">
                  <nz-icon nzType="user" nzTheme="outline"></nz-icon>
                  Mon profil
                </li>
                <li nz-menu-item (click)="navigateToSettings()">
                  <nz-icon nzType="setting" nzTheme="outline"></nz-icon>
                  Paramètres
                </li>
                <li nz-menu-divider></li>
                <li nz-menu-item (click)="onLogout()" class="logout-item">
                  <nz-icon nzType="logout" nzTheme="outline"></nz-icon>
                  Se déconnecter
                </li>
              </ul>
            </nz-dropdown-menu>
          </div>
        </div>
      </div>
    </nz-header>
    <nz-content>
      <div class="inner-content">
        <router-outlet></router-outlet>
      </div>
    </nz-content>
  </nz-layout>
</nz-layout>
</ng-template>
