import { Injectable, signal } from '@angular/core';
import { Observable, of, throwError, BehaviorSubject } from 'rxjs';
import { delay, map, tap, catchError } from 'rxjs/operators';
import { Router } from '@angular/router';
import { 
  User, 
  AuthState, 
  LoginRequest, 
  LoginResponse, 
  Tenant, 
  UserRole, 
  AuthError, 
  AuthErrorCode 
} from '../models/auth.model';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly STORAGE_KEY = 'workeem_auth';
  private readonly SESSION_TIMEOUT = 8 * 60 * 60 * 1000; // 8 heures en millisecondes

  // Signaux pour la réactivité
  private authStateSignal = signal<AuthState>({
    isAuthenticated: false,
    user: null,
    token: null,
    refreshToken: null,
    expiresAt: null
  });

  // BehaviorSubject pour les observables
  private authStateSubject = new BehaviorSubject<AuthState>(this.authStateSignal());

  // Getters publics
  get authState() { return this.authStateSignal.asReadonly(); }
  get authState$() { return this.authStateSubject.asObservable(); }
  get isAuthenticated() { return this.authStateSignal().isAuthenticated; }
  get currentUser() { return this.authStateSignal().user; }
  get currentTenant() { return this.authStateSignal().user?.tenantName || 'Workeem'; }

  // Données mockées pour les tenants
  private mockTenants: Tenant[] = [
    {
      id: 'syllabus',
      name: 'syllabus',
      displayName: 'Syllabus Coworking',
      logo: 'assets/images/logo.png',
      primaryColor: '#6E56CF',
      secondaryColor: '#8B7ED8',
      address: 'Technopolis, Hay Riad, Rabat',
      phone: '+212 5 37 12 34 56',
      email: '<EMAIL>',
      website: 'www.syllabus.ma',
      settings: {
        allowSelfRegistration: false,
        requireEmailVerification: true,
        sessionTimeout: 480, // 8 heures
        maxLoginAttempts: 5,
        lockoutDuration: 30,
        passwordPolicy: {
          minLength: 8,
          requireUppercase: false,
          requireLowercase: true,
          requireNumbers: true,
          requireSpecialChars: false
        }
      },
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-15')
    }
  ];

  // Utilisateurs mockés
  private mockUsers: User[] = [
    {
      id: '1',
      email: '<EMAIL>',
      firstName: 'Youssef',
      lastName: 'El Amrani',
      role: UserRole.ADMIN,
      tenantId: 'syllabus',
      tenantName: 'Syllabus Coworking',
      avatar: 'assets/images/avatar-default.png',
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-15')
    }
  ];

  // Comptes de connexion mockés
  private mockAccounts = [
    {
      email: '<EMAIL>',
      password: 'password123',
      userId: '1'
    }
  ];

  constructor(private router: Router) {
    this.initializeAuth();
    this.startSessionMonitoring();
  }

  private initializeAuth() {
    const savedAuth = localStorage.getItem(this.STORAGE_KEY);
    if (savedAuth) {
      try {
        const authData = JSON.parse(savedAuth);
        if (this.isValidSession(authData)) {
          this.updateAuthState(authData);
        } else {
          this.clearAuthData();
        }
      } catch (error) {
        console.error('Erreur lors de la récupération des données d\'authentification:', error);
        this.clearAuthData();
      }
    }
  }

  private isValidSession(authData: AuthState): boolean {
    if (!authData.expiresAt) return false;
    return new Date(authData.expiresAt) > new Date();
  }

  private updateAuthState(newState: AuthState) {
    this.authStateSignal.set(newState);
    this.authStateSubject.next(newState);
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(newState));
  }

  private clearAuthData() {
    const emptyState: AuthState = {
      isAuthenticated: false,
      user: null,
      token: null,
      refreshToken: null,
      expiresAt: null
    };
    this.updateAuthState(emptyState);
    localStorage.removeItem(this.STORAGE_KEY);
  }

  private startSessionMonitoring() {
    setInterval(() => {
      const currentState = this.authStateSignal();
      if (currentState.isAuthenticated && !this.isValidSession(currentState)) {
        this.logout(true); // Déconnexion automatique
      }
    }, 60000); // Vérifier toutes les minutes
  }

  // Méthode de connexion
  login(credentials: LoginRequest): Observable<LoginResponse> {
    return of(credentials).pipe(
      delay(1000), // Simulation d'un appel API
      map(creds => {
        // Vérifier les identifiants
        const account = this.mockAccounts.find(
          acc => acc.email === creds.email && acc.password === creds.password
        );

        if (!account) {
          throw {
            code: AuthErrorCode.INVALID_CREDENTIALS,
            message: 'Email ou mot de passe incorrect'
          } as AuthError;
        }

        // Récupérer l'utilisateur
        const user = this.mockUsers.find(u => u.id === account.userId);
        if (!user) {
          throw {
            code: AuthErrorCode.UNKNOWN_ERROR,
            message: 'Utilisateur non trouvé'
          } as AuthError;
        }

        // Créer la réponse de connexion
        const expiresAt = new Date(Date.now() + this.SESSION_TIMEOUT);
        const response: LoginResponse = {
          user: {
            ...user,
            lastLoginAt: new Date()
          },
          token: this.generateToken(),
          refreshToken: this.generateToken(),
          expiresAt
        };

        return response;
      }),
      tap(response => {
        // Mettre à jour l'état d'authentification
        const newState: AuthState = {
          isAuthenticated: true,
          user: response.user,
          token: response.token,
          refreshToken: response.refreshToken,
          expiresAt: response.expiresAt
        };
        this.updateAuthState(newState);
      }),
      catchError(error => {
        console.error('Erreur de connexion:', error);
        return throwError(() => error);
      })
    );
  }

  // Méthode de déconnexion
  logout(isAutoLogout: boolean = false): Observable<boolean> {
    return of(true).pipe(
      delay(300),
      tap(() => {
        this.clearAuthData();
        if (!isAutoLogout) {
          this.router.navigate(['/login']);
        } else {
          this.router.navigate(['/login'], { 
            queryParams: { expired: 'true' } 
          });
        }
      })
    );
  }

  // Vérifier si l'utilisateur est connecté
  isLoggedIn(): boolean {
    const state = this.authStateSignal();
    return state.isAuthenticated && this.isValidSession(state);
  }

  // Obtenir le tenant par nom
  getTenantByName(tenantName: string): Observable<Tenant | null> {
    const tenant = this.mockTenants.find(t => t.name === tenantName);
    return of(tenant || null).pipe(delay(200));
  }

  // Générer un token fictif
  private generateToken(): string {
    return btoa(Math.random().toString(36).substring(2) + Date.now().toString(36));
  }

  // Rafraîchir le token
  refreshToken(): Observable<string> {
    const currentState = this.authStateSignal();
    if (!currentState.refreshToken) {
      return throwError(() => ({ 
        code: AuthErrorCode.INVALID_TOKEN, 
        message: 'Token de rafraîchissement manquant' 
      }));
    }

    return of(this.generateToken()).pipe(
      delay(500),
      tap(newToken => {
        const newState: AuthState = {
          ...currentState,
          token: newToken,
          expiresAt: new Date(Date.now() + this.SESSION_TIMEOUT)
        };
        this.updateAuthState(newState);
      })
    );
  }

  // Obtenir les informations du tenant actuel
  getCurrentTenantInfo(): Tenant | null {
    const user = this.currentUser;
    if (!user) return null;
    return this.mockTenants.find(t => t.id === user.tenantId) || null;
  }
}
