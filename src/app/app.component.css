:host {
  display: flex;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.app-layout {
  height: 100vh;
  background-color: #F2F2F7;
}

.menu-sidebar {
  position: fixed !important;
  left: 0;
  top: 0;
  z-index: 10;
  height: 100vh !important;
  background-color: #FFFFFF !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-right: 1px solid #E5E5EA;
  overflow-y: auto;
}

.header-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: transparent;
  border: 1px solid transparent;
  color: #1C1C1E;
}

.header-trigger:hover {
  background: #F8F9FA;
  border-color: #E5E5EA;
  color: #6E56CF;
}

.trigger {
  font-size: 18px;
  transition: color 0.2s ease;
}

/* Logo Section */
.sidebar-logo {
  position: relative;
  height: 80px;
  padding: 16px 24px;
  overflow: hidden;
  background: #FFFFFF;
  border-bottom: 1px solid #E5E5EA;
  transition: all 0.3s ease;
}

.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.logo-image {
  height: 50px;
  width: auto;
  max-width: 220px;
  border-radius: 8px;
  object-fit: contain;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.logo-image.logo-collapsed {
  height: 48px;
  width: 48px;
  max-width: 48px;
}

/* Menu Styles */
.main-menu {
  border: none !important;
  background: transparent !important;
  padding: 16px 8px 100px 8px; /* Padding bottom pour le footer */
}

.menu-item {
  margin: 4px 8px !important;
  border-radius: 8px !important;
  height: 48px !important;
  line-height: 48px !important;
  color: #1C1C1E !important;
  font-weight: 500;
  transition: all 0.2s ease;
  padding: 0 16px !important;
  width: calc(100% - 16px) !important;
  display: flex !important;
  align-items: center !important;
}

.menu-item:hover {
  background-color: #EDE9F8 !important;
  color: #6E56CF !important;
}

.menu-item.ant-menu-item-selected {
  background-color: #6E56CF !important;
  color: #FFFFFF !important;
}

.menu-item .anticon {
  font-size: 18px;
  margin-right: 12px;
  min-width: 18px;
}

/* Sidebar Footer */
.sidebar-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  border-top: 1px solid #E5E5EA;
  background: #FFFFFF;
}

.sidebar-footer.collapsed {
  padding: 16px 8px;
}

.tenant-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.tenant-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #EDE9F8;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6E56CF;
  font-size: 18px;
  flex-shrink: 0;
}

.tenant-details {
  flex: 1;
  min-width: 0;
}

.tenant-name {
  font-size: 14px;
  font-weight: 600;
  color: #1C1C1E;
  line-height: 1.2;
  margin-bottom: 2px;
}

.tenant-type {
  font-size: 12px;
  color: #8E8E93;
  line-height: 1.2;
}

/* Header Styles */
nz-header {
  position: fixed !important;
  top: 0;
  right: 0;
  padding: 0;
  width: calc(100% - 280px) !important;
  z-index: 5;
  background: #FFFFFF !important;
  border-bottom: 1px solid #E5E5EA;
  transition: width 0.2s ease;
}

.app-header {
  position: relative;
  height: 64px;
  padding: 0 24px;
  background: #FFFFFF;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
}

/* User Profile */
.user-profile {
  position: relative;
}

.user-info-container {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  border-radius: 20px;
  background: #F8F9FA;
  border: 1px solid #E5E5EA;
  transition: all 0.2s ease;
  min-width: 160px;
  cursor: pointer;
}

.user-info-container:hover {
  background-color: #F0F0F5;
  border-color: #6E56CF;
}

.user-avatar-img {
  flex-shrink: 0;
}

.dropdown-icon {
  font-size: 12px;
  color: #8E8E93;
  transition: transform 0.3s ease;
}

.user-info-container:hover .dropdown-icon {
  color: #6E56CF;
}

.user-info {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: #1C1C1E;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-role {
  font-size: 12px;
  color: #8E8E93;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}



/* Content Styles */
nz-content {
  margin-left: 280px !important;
  margin-top: 64px !important;
  padding: 24px;
  background: #F2F2F7 !important;
  min-height: calc(100vh - 64px);
  transition: margin-left 0.2s ease;
}

.inner-content {
  padding: 0;
  background: transparent;
  min-height: calc(100vh - 112px);
  padding-bottom: 24px;
}

/* Sidebar collapsed styles */
.menu-sidebar.ant-layout-sider-collapsed {
  width: 80px !important;
}

/* Header adjustment for collapsed sidebar */
.app-layout.sidebar-collapsed nz-header {
  width: calc(100% - 80px) !important;
}

.app-layout.sidebar-collapsed nz-content {
  margin-left: 80px !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .menu-sidebar {
    position: fixed !important;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .menu-sidebar.ant-layout-sider-collapsed {
    transform: translateX(-100%);
  }

  .app-layout.sidebar-open .menu-sidebar {
    transform: translateX(0);
  }

  nz-header {
    width: 100% !important;
    left: 0 !important;
  }

  nz-content {
    margin-left: 0 !important;
    padding: 12px;
  }

  .inner-content {
    padding: 0;
    padding-bottom: 12px;
  }

  /* User profile responsive */
  .user-profile {
    min-width: 48px;
    padding: 6px 8px;
  }

  .user-info {
    display: none;
  }

  .user-avatar {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
}

/* Menu dropdown utilisateur */
.ant-dropdown-menu-item {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  padding: 8px 16px !important;
  font-size: 14px !important;
}

.ant-dropdown-menu-item:hover {
  background-color: #F5F5F7 !important;
}

.logout-item {
  color: #FF3B30 !important;
}

.logout-item:hover {
  background-color: #FFF5F5 !important;
  color: #FF3B30 !important;
}

.logout-item nz-icon {
  color: #FF3B30 !important;
}
